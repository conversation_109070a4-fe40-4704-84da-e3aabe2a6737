"""empty message

Revision ID: 2bbb03139d6d
Revises: 
Create Date: 2023-12-11 20:55:37.630063

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "2bbb03139d6d"
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("category_update_request", schema=None) as batch_op:
        batch_op.add_column(
            sa.Column("update_description1", sa.String(), nullable=False)
        )
        batch_op.add_column(
            sa.Column("update_description2", sa.String(), nullable=False)
        )
        batch_op.drop_constraint(None, type_="foreignkey")
        batch_op.drop_column("branch_id")
        batch_op.drop_column("datetime")
        batch_op.drop_column("update_description")

    with op.batch_alter_table("store_manager", schema=None) as batch_op:
        batch_op.alter_column("isApproved", existing_type=sa.VARCHAR(), nullable=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("store_manager", schema=None) as batch_op:
        batch_op.alter_column("isApproved", existing_type=sa.VARCHAR(), nullable=True)

    with op.batch_alter_table("category_update_request", schema=None) as batch_op:
        batch_op.add_column(
            sa.Column("update_description", sa.VARCHAR(), nullable=True)
        )
        batch_op.add_column(sa.Column("datetime", sa.VARCHAR(), nullable=False))
        batch_op.add_column(sa.Column("branch_id", sa.INTEGER(), nullable=False))
        batch_op.create_foreign_key(None, "store_manager", ["branch_id"], ["branch_id"])
        batch_op.drop_column("update_description2")
        batch_op.drop_column("update_description1")

    # ### end Alembic commands ###
