{% extends "form.html" %} {% block title %}Search Products{% endblock %} {%
block heading %}
<h1 class="heading">Grocery Store Search</h1>
<br />{% endblock %} {% block inputs %}
<input
  type="text"
  class="form-control"
  placeholder="Product name"
  name="name"
  autofocus
/><br />
<select class="form-control" name="category" required>
  <option value="">Select category</option>
  <option value="all">All</option>
  {% for category in categories %}
  <option value="{{ category['category_id'] }}">{{ category['name'] }}</option>
  {% endfor %}</select
><br />

<label for="price-range">Price range:</label>
<input
  type="number"
  step="0.01"
  class="form-control"
  placeholder="Minimum price"
  name="min_price"
  id="price-range"
/>
<input
  type="number"
  step="0.01"
  class="form-control"
  placeholder="Maximum price"
  name="max_price"
/><br />

<label for="avg_rating-range">Average Rating range:</label>
<input
  type="number"
  step="0.01"
  class="form-control"
  placeholder="Minimum avg_rating"
  name="min_avg_rating"
  id="avg_rating-range"
/>
<input
  type="number"
  step="0.01"
  class="form-control"
  placeholder="Maximum avg_rating"
  name="max_avg_rating"
/><br />

<label for="manufacture-date">Manufacture date range:</label>
<input
  type="date"
  class="form-control"
  name="min_manufacture_date"
  id="manufacture-date"
/>
<input type="date" class="form-control" name="max_manufacture_date" /><br />

<label for="expiry-date">Expiry date range:</label>
<input
  type="date"
  class="form-control"
  name="min_expiry_date"
  id="expiry-date"
/>
<input type="date" class="form-control" name="max_expiry_date" /><br />
{% endblock %} {% block submitButtons %}
<button class="btn" type="submit">Search</button>
<h3><a href="/customer/{{ c_id }}/dashboard">Dashboard</a></h3>
{% endblock %}
