<!DOCTYPE html>
<html>
<head>
  <title>{{ product['name'] }}</title>
  <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css">
  <style>
    .section {
      flex-basis: 20%;
      border: 1px solid #000;
      padding: 10px;
      text-align: center;
    }

    .product {
      margin-top: 10px;
    }
  </style>
</head>
<body>
  <div class="container">
    <header>
      <br>
      <h1 class="text-center">Product : {{ product['name'] }}</h1>
      <a href="/customer/{{ c_id }}/searchProducts">Back to search</a> <br>
      <a href="/customer/{{ c_id }}/dashboard">Back to Dashboard</a> 
      <br>
    </header>
    <br>
    <div class="row">
      <div class="col-md-4">
          <img src="{{ product['image_url'] }}" alt="Product Image" class="img-fluid">
      </div>

      <div class="col-md-4">
          {% if product['quantity']==0 %}
              <h3 style="color: red;">Out of Stock</h3>
          {% endif %}
          <h2>{{ product['name'] }}</h2>
          <br>
          <h6>
          <p><strong>Average rating :</strong> {{ product['avg_rating'] }} &#9733;</p>
          <br>
          <p><strong>Price :</strong> {{ product['price'] }}</p>
          <br>
          <p><strong>Minimum quantity :</strong> {{ product['price'] / product['pricePerUnit'] }} {{ product['unit'] }}</p>
          <br>
          <p><strong>Price per unit : </strong>{{ product['pricePerUnit'] }} per {{ product['unit'] }}</p>
          <br>
          </h6>
          <p><strong>Life : </strong>{{ product['manufacture_date'] }} to {{ product['expiry_date'] }}</p>
          <br>
          <p><strong>Description : </strong>{{ product['description'] }}</p>
      </div>

      <div class="col-md-4">
          <form action="/customer/{{ c_id }}/searchResults" method="POST">
              <p><strong>Quantity :</strong> <select name="quantity" id="quantity" required>
                  {% for number in range(1, 11) %}
                      <option value="{{ number }}">{{ number }}</option>
                  {% endfor %}
              </select></p>
              <h4>
              {% if product['quantity']==0 %}
                  <button type="submit" disabled>Add to cart</button>
              {% else %}
                  <input type="hidden" name="product_id" value="{{ product['product_id'] }}">
                  <input type="hidden" name="c_id" value="{{ c_id }}">
                  <button type="submit">Add to cart</button>
              {% endif %}
              </h4>
          </form>
      </div>
    </div>

    <br>
    <br>
    <hr>

    
      <h1>Similar products</h1>
      <div class="row">
        {% for sim_prod in similar_products %}  
          <div class="col-md-2">
            <div class="section">
              <img src="{{ sim_prod['image_url'] }}" alt="Product Image" class="img-fluid">
              <div class="product">
                <h2 class="product-name"><a href="/customer/{{ c_id }}/productDetails/{{ sim_prod['product_id'] }}">{{ sim_prod['name'] }}</a></h2>
                <h4>{{ sim_prod['price'] }}</h4>
                {% if sim_prod['quantity']==0 %}
                <form>
                  <button type="submit" disabled>Add to cart</button>
                </form>
                {% else %}
                <form action="/customer/{{ c_id }}/searchResults" method="POST">
                  <input type="hidden" name="product_id" value="{{ sim_prod['product_id'] }}">
                  <input type="hidden" name="c_id" value="{{ c_id }}">
                  <input type="hidden" name="quantity" value="1">
                  <button type="submit">Add to cart</button>
              </form>
                {% endif %}
              </div>
            </div>
          </div>
        {% endfor %}
      </div>
  
  


    <br>
    <br>
    <hr>

    <form action="/customer/{{ c_id }}/productDetails/{{ product['product_id'] }}" method="POST">
        <div class="card">
            <div class="card-body">
            <h3 class="card-title">Write a review </h3>
            <p class="card-text">
                <label for="rating">Rating:</label>
                <select id="rating" name="rating" required>
                  {% for number in range(1, 6) %}
                    <option value="{{ number }}">{{ number }}</option>
                  {% endfor %}
                </select>
                <br>
                <input type="text" name="review_text" placeholder="Write the review" required>
                <button type="submit">Submit</button>
            </p>  
        </div> 
    </form>

    <section class="mt-4">
      {% for review in reviews %}
        <div class="card">
          <div class="card-body">
            <h5 class="card-title">{{ review.customer_name }}{% for _ in range(review.stars) %}&#9733;{% endfor %}</h5>
            <h6 class="card-sub-text">{{ review.date }}</h6>
            <p class="card-text">{{ review.review_text }}</p>
          </div>
        </div>
      {% endfor %}
    </section>
  </div>
</body>
</html>
