{% extends "form.html" %} {% block title %}Edit Product{% endblock %} {% block
heading %}
<h1 class="heading">Grocery Store</h1>
{% endblock %} {% block formHeading %}
<h1 class="form-heading">Edit Product</h1>
{% endblock %} {% block inputs %}
<input
  type="text"
  class="form-control"
  placeholder="Name"
  value="{{ product['name'] }}"
  name="name"
  required
  autofocus
/><br />
<input
  type="text"
  class="form-control"
  placeholder="Description"
  value="{{ product['description'] }}"
  name="description"
  required
/><br />
<input
  type="number"
  step="0.01"
  class="form-control"
  placeholder="Price"
  value="{{ product['price'] }}"
  name="price"
  required
/><br />
<input
  type="number"
  step="0.01"
  class="form-control"
  placeholder="Quantity"
  value="{{ product['quantity'] }}"
  name="quantity"
  required
/><br />
<input
  type="text"
  class="form-control"
  placeholder="Unit"
  value="{{ product['unit'] }}"
  name="unit"
  required
/><br />
<input
  type="number"
  step="0.01"
  class="form-control"
  placeholder="pricePerUnit"
  value="{{ product['pricePerUnit'] }}"
  name="pricePerUnit"
  required
/><br />
<input
  type="number"
  class="form-control"
  placeholder="category_id"
  value="{{ product['category_id'] }}"
  name="category_id"
  required
/><br />
<label for="manufacture-date">Manufacture date :</label>
<input
  type="date"
  class="form-control"
  placeholder="manufacture_date"
  value="{{ product['manufacture_date'] }}"
  name="manufacture_date"
  required
/><br />
<label for="expiry-date">Expiry date :</label>
<input
  type="date"
  class="form-control"
  placeholder="expiry_date"
  value="{{ product['expiry_date'] }}"
  name="expiry_date"
  required
/><br />
<input
  type="text"
  class="form-control"
  placeholder="image_url"
  value="{{ product['image_url'] }}"
  name="image_url"
  required
/><br />
<br />
{% endblock %} {% block submitButtons %}
<button class="btn" type="submit">Save changes</button>
{% endblock %}
