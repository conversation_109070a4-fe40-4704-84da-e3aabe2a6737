<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Admin Dashboard</title>
    <style>
      .container {
        display: flex;
        justify-content: space-between;
      }
      .left-column {
        width: 66%;
        background-color: #f2db80;
        padding: 20px;
        border-radius: 10px;
      }
      .right-column {
        width: 34%;
        align-items: center;
        text-align: center;
        background-color: #f2db80;
        padding: 20px;
        border-radius: 10px;
        margin-left: 20px;
      }
      table {
        border-collapse: collapse;
        width: 100%;
      }
      td {
        padding: 10px;
        text-align: left;
        border-bottom: 1px solid #ddd;
      }
    </style>
  </head>
  <body>
    <h1 style="text-align: center">Welcome to the Admin Dashboard</h1>
    <div class="container">
      <div class="left-column">
        <strong>Username:</strong> {{ username }} <br />
        <strong
          >You are the only Admin of this app. Following are the Sign up
          requests from different Store Managers. Please confirm the details and
          approve/reject the requests accordingly.</strong
        >
        <br />
      </div>

      <div class="right-column">
        <button
          onclick="window.location.href='/admin/{{ admin_id }}/categoryUpdateRequests'"
        >
          Pending updates
        </button>
        <br /><br />
        <button
          onclick="window.location.href='/admin/{{ admin_id }}/updateHistory'"
        >
          Update history
        </button>
        <br /><br />
        <button onclick="window.location.href='/admin/{{ admin_id }}/Category'">
          Go to Categories
        </button>
        <br /><br />
        <button onclick="window.location.href='/admin/{{ admin_id }}/Product'">
          Go to Products
        </button>
        <br /><br />
        <button onclick="window.location.href='/signout'">Sign-out</button>
      </div>
    </div>

    {% if pending %}
    <h3>Pending Store Managers :</h3>
    <form action="/admin/{{ admin_id }}/dashboard" method="post">
      <table>
        <thead>
          <tr>
            <td>Name</td>
            <td>Store Manager IDs</td>
            <td>Branch IDs</td>
            <td>Email</td>
            <td>Current Status</td>
            <td>Updated Status</td>
          </tr>
        </thead>
        <tbody>
          {% for i in pending %}
          <tr>
            <td>{{ i["name"] }}</td>
            <td>{{ i["store_manager_id"] }}</td>
            <td>{{ i["branch_id"] }}</td>
            <td>{{ i["email"] }}</td>
            <td>{{ i["isApproved"] }}</td>
            <td>
              <select name="status_{{ i['store_manager_id'] }}">
                <option value="Approved">Approve</option>
                <option value="Rejected">Reject</option>
                <option value="Pending" selected>Pending</option>
              </select>
            </td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
      <button type="submit">Update Changes</button>
    </form>
    {% else %}
    <h3>No pending Store Managers!</h3>
    {% endif %}
  </body>
</html>
